<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>价格计算器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 2.5em;
      margin-bottom: 10px;
      font-weight: 300;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
    }

    .content {
      padding: 40px;
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 1.1em;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 12px 15px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 1em;
      transition: all 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #4facfe;
      box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    }

    .calculate-btn {
      width: 100%;
      padding: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 1.2em;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;
    }

    .calculate-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .result {
      margin-top: 30px;
      padding: 25px;
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      border-radius: 10px;
      display: none;
    }

    .result.show {
      display: block;
      animation: slideIn 0.5s ease;
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .result h3 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.4em;
    }

    .calculation-detail {
      background: white;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 15px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .calculation-step {
      margin-bottom: 10px;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .calculation-step:last-child {
      border-bottom: none;
      font-weight: 600;
      font-size: 1.1em;
      color: #667eea;
    }

    .final-price {
      font-size: 2em;
      font-weight: bold;
      color: #667eea;
      text-align: center;
      margin-top: 15px;
    }

    .error {
      color: #e74c3c;
      font-size: 0.9em;
      margin-top: 5px;
    }

    .input-group {
      display: flex;
      gap: 15px;
    }

    .input-group .form-group {
      flex: 1;
    }

    @media (max-width: 600px) {
      .input-group {
        flex-direction: column;
      }

      .content {
        padding: 20px;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 2em;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>价格计算器</h1>
      <p>成本价 + 版式费 = 最终价格</p>
    </div>

    <div class="content">
      <form id="priceForm">
        <div class="form-group">
          <label for="baseValue">输入A值（正数）:</label>
          <input type="number" id="baseValue" min="0.01" step="0.01" required>
          <div class="error" id="baseValueError"></div>
        </div>

        <div class="input-group">
          <div class="form-group">
            <label for="layoutPrice">版式单价:</label>
            <select id="layoutPrice" required>
              <option value="">请选择版式规格</option>
              <option value="8">90-54以下 - ¥8</option>
              <option value="13">90-54以上140-100以下 - ¥13</option>
              <option value="20">140-100以上285-210以下 - ¥20</option>
              <option value="25">285-210以上420-285以下 - ¥25</option>
              <option value="35">420-285以上570-420以下 - ¥35</option>
              <option value="50">570-420以上 - ¥50</option>
            </select>
          </div>

          <div class="form-group">
            <label for="layoutQuantity">版式数量:</label>
            <input type="number" id="layoutQuantity" min="1" step="1" required>
          </div>
        </div>

        <button type="submit" class="calculate-btn">计算价格</button>
      </form>

      <div class="result" id="result">
        <h3>价格计算详情</h3>
        <div class="calculation-detail" id="calculationDetail">
        </div>
        <div class="final-price" id="finalPrice"></div>
      </div>
    </div>
  </div>

  <script>
    // 倍率区间配置
    const multiplierRanges = [
      { min: 0, max: 10.1, multiplier: 2.2 },
      { min: 10.1, max: 20, multiplier: 1.8 },
      { min: 20, max: 30, multiplier: 1.7 },
      { min: 30, max: 40, multiplier: 1.6 },
      { min: 40, max: 120, multiplier: 1.55 },
      { min: 120, max: 200, multiplier: 1.5 },
      { min: 200, max: 400, multiplier: 1.45 },
      { min: 400, max: 700, multiplier: 1.4 },
      { min: 700, max: 1000, multiplier: 1.35 },
      { min: 1000, max: 2000, multiplier: 1.3 },
      { min: 2000, max: 10000, multiplier: 1.25 },
      { min: 10000, max: Infinity, multiplier: 1.2 }
    ];

    function getMultiplier (value) {
      for (let range of multiplierRanges) {
        if (value > range.min && value <= range.max) {
          return range.multiplier;
        }
      }
      return 1.2; // 默认倍率
    }

    function calculatePrice () {
      const baseValue = parseFloat(document.getElementById('baseValue').value);
      const layoutPrice = parseFloat(document.getElementById('layoutPrice').value);
      const layoutQuantity = parseInt(document.getElementById('layoutQuantity').value);

      // 清除之前的错误信息
      document.getElementById('baseValueError').textContent = '';

      // 验证输入
      if (!baseValue || baseValue <= 0) {
        document.getElementById('baseValueError').textContent = '请输入有效的正数';
        return;
      }

      if (!layoutPrice || !layoutQuantity) {
        alert('请填写完整的版式信息');
        return;
      }

      // 计算成本价
      const multiplier = getMultiplier(baseValue);
      const rawCostPrice = baseValue * multiplier;
      const costPrice = Math.max(rawCostPrice, 15); // 最低15

      // 计算版式费
      const layoutFee = layoutPrice * layoutQuantity;

      // 计算最终价格
      const finalPrice = costPrice + layoutFee;

      // 显示计算详情
      const calculationDetail = document.getElementById('calculationDetail');
      calculationDetail.innerHTML = `
        <div class="calculation-step">
          <strong>第一步：计算成本价</strong>
        </div>
        <div class="calculation-step">
          A值: ${baseValue}
        </div>
        <div class="calculation-step">
          倍率: ${multiplier} (根据A值 ${baseValue} 从区间获取)
        </div>
        <div class="calculation-step">
          原始成本价: ${baseValue} × ${multiplier} = ${rawCostPrice.toFixed(2)}
        </div>
        <div class="calculation-step">
          实际成本价: ${costPrice.toFixed(2)} ${rawCostPrice < 15 ? '(最低15元)' : ''}
        </div>
        <div class="calculation-step">
          <strong>第二步：计算版式费</strong>
        </div>
        <div class="calculation-step">
          版式单价: ¥${layoutPrice}
        </div>
        <div class="calculation-step">
          版式数量: ${layoutQuantity}
        </div>
        <div class="calculation-step">
          版式费: ¥${layoutPrice} × ${layoutQuantity} = ¥${layoutFee.toFixed(2)}
        </div>
        <div class="calculation-step">
          <strong>最终价格: ¥${costPrice.toFixed(2)} + ¥${layoutFee.toFixed(2)} = ¥${finalPrice.toFixed(2)}</strong>
        </div>
      `;

      document.getElementById('finalPrice').textContent = `¥${finalPrice.toFixed(2)}`;
      document.getElementById('result').classList.add('show');
    }

    // 表单提交事件
    document.getElementById('priceForm').addEventListener('submit', function (e) {
      e.preventDefault();
      calculatePrice();
    });

    // 实时计算（可选）
    document.getElementById('baseValue').addEventListener('input', function () {
      if (this.value && document.getElementById('layoutPrice').value && document.getElementById('layoutQuantity').value) {
        calculatePrice();
      }
    });

    document.getElementById('layoutPrice').addEventListener('change', function () {
      if (document.getElementById('baseValue').value && this.value && document.getElementById('layoutQuantity').value) {
        calculatePrice();
      }
    });

    document.getElementById('layoutQuantity').addEventListener('input', function () {
      if (document.getElementById('baseValue').value && document.getElementById('layoutPrice').value && this.value) {
        calculatePrice();
      }
    });
  </script>
</body>

</html>